#!/bin/bash

# ScreenHider Enhanced Build Script
# This script compiles the Swift source files and creates a macOS app bundle
# with optional code signing and distribution features

set -e  # Exit on any error

# Configuration
APP_NAME="ScreenHider"
BUNDLE_ID="com.screenhider.app"
VERSION="1.1.0"
MIN_MACOS="11.0"

# Build options (can be set via environment variables)
BUILD_TYPE="${BUILD_TYPE:-debug}"  # debug or release
SIGN_APP="${SIGN_APP:-false}"      # true to enable code signing
NOTARIZE="${NOTARIZE:-false}"      # true to enable notarization
UNIVERSAL="${UNIVERSAL:-false}"    # true to build universal binary

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Print header
echo "🔨 Building ScreenHider v${VERSION}"
echo "   Build Type: ${BUILD_TYPE}"
echo "   Code Signing: ${SIGN_APP}"
echo "   Universal Binary: ${UNIVERSAL}"
echo ""

# Check if we're in the right directory
if [ ! -f "main.swift" ]; then
    log_error "main.swift not found. Please run this script from the Screen_hider directory."
    exit 1
fi

# Check if Swift compiler is available
if ! command -v swiftc &> /dev/null; then
    log_error "Swift compiler not found. Please install Xcode or Swift toolchain."
    exit 1
fi

# Display Swift version
SWIFT_VERSION=$(swiftc --version | head -n1)
log_info "Using: ${SWIFT_VERSION}"

# Function to compile Swift files
compile_swift() {
    local target_arch="$1"
    local output_name="$2"

    log_info "Compiling Swift files for ${target_arch}..."

    # Set compilation flags based on build type
    local swift_flags=""
    if [ "$BUILD_TYPE" = "release" ]; then
        swift_flags="-O -whole-module-optimization"
    else
        swift_flags="-g"
    fi

    # Add target architecture if specified
    if [ -n "$target_arch" ]; then
        swift_flags="$swift_flags -target $target_arch"
    fi

    swiftc -o "$output_name" \
        main.swift \
        AppDelegate.swift \
        OverlayWindow.swift \
        ScreenManager.swift \
        -framework Cocoa \
        $swift_flags

    if [ $? -ne 0 ]; then
        log_error "Compilation failed for ${target_arch}!"
        exit 1
    fi

    log_success "Compilation successful for ${target_arch}"
}

# Clean previous build
log_info "Cleaning previous build..."
rm -f ScreenHider ScreenHider_arm64 ScreenHider_x86_64
rm -rf ScreenHider.app

# Compile based on architecture preference
if [ "$UNIVERSAL" = "true" ]; then
    log_info "Building universal binary..."

    # Compile for both architectures
    compile_swift "arm64-apple-macos${MIN_MACOS}" "ScreenHider_arm64"
    compile_swift "x86_64-apple-macos${MIN_MACOS}" "ScreenHider_x86_64"

    # Create universal binary
    log_info "Creating universal binary..."
    lipo -create -output ScreenHider ScreenHider_arm64 ScreenHider_x86_64

    # Clean up architecture-specific binaries
    rm ScreenHider_arm64 ScreenHider_x86_64

    log_success "Universal binary created"
else
    # Compile for current architecture
    compile_swift "" "ScreenHider"
fi

# Create app bundle structure
log_info "Creating app bundle structure..."
mkdir -p "${APP_NAME}.app/Contents/MacOS"
mkdir -p "${APP_NAME}.app/Contents/Resources"

# Copy executable
cp ScreenHider "${APP_NAME}.app/Contents/MacOS/${APP_NAME}"

# Copy Info.plist
cp Info.plist "${APP_NAME}.app/Contents/Info.plist"

# Set executable permissions
chmod +x "${APP_NAME}.app/Contents/MacOS/${APP_NAME}"

# Clean up temporary executable
rm ScreenHider

log_success "App bundle created: ${APP_NAME}.app"

# Code signing (if enabled)
if [ "$SIGN_APP" = "true" ]; then
    log_info "Code signing application..."

    # Check if signing identity is available
    if ! security find-identity -v -p codesigning | grep -q "Developer ID Application"; then
        log_warning "No Developer ID Application certificate found. Skipping code signing."
    else
        codesign --force --deep --sign "Developer ID Application" "${APP_NAME}.app"
        log_success "Application signed successfully"

        # Verify signature
        codesign --verify --verbose "${APP_NAME}.app"
        log_success "Signature verified"
    fi
fi

# Display file information
log_info "Application information:"
echo "   Size: $(du -sh "${APP_NAME}.app" | cut -f1)"
echo "   Bundle ID: ${BUNDLE_ID}"
echo "   Version: ${VERSION}"
echo "   Min macOS: ${MIN_MACOS}"

# Display architecture information
if command -v lipo &> /dev/null; then
    echo "   Architectures: $(lipo -archs "${APP_NAME}.app/Contents/MacOS/${APP_NAME}")"
fi

echo ""
log_success "Build completed successfully!"
echo ""
echo "🚀 To run the application:"
echo "   • Double-click ${APP_NAME}.app"
echo "   • Or run: open ${APP_NAME}.app"
echo "   • Or run: ./${APP_NAME}.app/Contents/MacOS/${APP_NAME}"
echo ""
echo "📋 Usage:"
echo "   • The app will appear in your menu bar with a rectangle icon"
echo "   • Left-click the icon to toggle overlay on/off"
echo "   • Right-click for full menu options"
echo "   • Use Cmd+Shift+H keyboard shortcut for quick toggle"
echo ""
echo "🔍 To test screen sharing:"
echo "   1. Enable the overlay from the menu bar"
echo "   2. Start screen sharing in Zoom/Teams/Meet"
echo "   3. The right quarter should be hidden from viewers"
echo "   4. You can still interact with that area normally"
echo ""
echo "🛠️  Build options:"
echo "   BUILD_TYPE=release ./build.sh     # Release build with optimizations"
echo "   UNIVERSAL=true ./build.sh         # Universal binary (Intel + Apple Silicon)"
echo "   SIGN_APP=true ./build.sh          # Code sign the application"
