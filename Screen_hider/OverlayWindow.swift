import Cocoa
import WebKit
import os.log
import CoreGraphics
import QuartzCore
import IOKit

/// A specialized NSWindow that creates a screen overlay for masking content
/// while remaining transparent to input and excluded from screen capture
class OverlayWindow: NSWindow, WKNavigationDelegate {

    // MARK: - Properties

    /// Logger for this class
    private let logger = Logger(subsystem: "com.screenhider.app", category: "OverlayWindow")

    /// The color of the overlay (default: black)
    var overlayColor: NSColor = .black {
        didSet {
            updateOverlayAppearance()
        }
    }

    /// The opacity of the overlay (0.0 to 1.0)
    var overlayOpacity: CGFloat = 0.7 {
        didSet {
            alphaValue = overlayOpacity
        }
    }

    /// Whether the overlay is currently visible
    private(set) var isOverlayVisible: Bool = false

    /// Whether the overlay is in resize mode (shows resize handles)
    private(set) var isResizeMode: Bool = false

    /// Whether the overlay is in workspace mode (shows web view and controls)
    private(set) var isWorkspaceMode: Bool = false

    /// Web view for displaying content within the overlay
    private var webView: WKWebView?

    /// Toolbar view for workspace controls
    private var workspaceToolbar: NSView?

    /// Screen change observer token
    private var screenChangeObserver: NSObjectProtocol?
    
    // MARK: - Initialization

    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: contentRect, styleMask: style, backing: backingStoreType, defer: flag)
        logger.info("🪟 Initializing OverlayWindow")
        setupOverlayWindow()
    }

    /// Configures the window with all necessary properties for screen masking
    private func setupOverlayWindow() {
        logger.debug("⚙️ Setting up overlay window configuration")

        // Basic window configuration - make it resizable like a normal window
        self.styleMask = [.resizable, .titled, .closable, .miniaturizable]
        self.backgroundColor = overlayColor
        self.isOpaque = false
        self.hasShadow = true
        self.ignoresMouseEvents = false  // Allow mouse events for dragging/resizing
        self.alphaValue = overlayOpacity
        self.title = "ScreenHider Overlay"

        // Window level - use a very high level that's above screen sharing
        // Use a custom high level that's above most capture methods
        self.level = NSWindow.Level(rawValue: Int(CGWindowLevelForKey(.screenSaverWindow)) + 1000)

        // Collection behavior for screen capture exclusion while maintaining visibility
        self.collectionBehavior = [
            .canJoinAllSpaces,           // Appears on all Spaces
            .fullScreenAuxiliary,        // Appears over full screen apps
            .stationary,                 // Doesn't participate in Exposé
            .ignoresCycle               // Not included in window cycling
        ]

        // Critical: Exclude from screen capture and sharing
        enableScreenCaptureExclusion()

        // Make sure the window doesn't appear in mission control
        self.isExcludedFromWindowsMenu = true


        // Set up the content view
        setupContentView()

        // Set up screen change monitoring
        setupScreenChangeObservers()

        // Start periodic verification of screen capture exclusion
        startPeriodicExclusionVerification()

        logger.info("✅ Overlay window setup complete with enhanced screen capture exclusion")
    }

    /// Enables comprehensive screen capture exclusion using multiple methods
    private func enableScreenCaptureExclusion() {
        if #available(macOS 11.0, *) {
            // Method 1: Primary exclusion API
            self.sharingType = .none
            logger.info("✅ Primary exclusion: sharingType = .none")

            // Method 2: Window server level exclusion
            self.isExcludedFromWindowsMenu = true

            // Method 3: Use CGWindow APIs to hide from capture
            applyCGWindowExclusion()

            // Method 4: Set window properties that prevent capture
            applyAdvancedExclusionProperties()

            logger.info("✅ Multi-layer screen capture exclusion configured")

        } else {
            logger.warning("⚠️ Screen capture exclusion not available on this macOS version")
        }
    }

    /// Applies CGWindow-level exclusion (more reliable for some apps)
    private func applyCGWindowExclusion() {
        // Get the window number for CGWindow APIs
        let windowNumber = self.windowNumber

        // Use CGWindow APIs to exclude from capture
        // This is a lower-level approach that some screen sharing apps respect more
        if windowNumber > 0 {
            logger.info("✅ CGWindow exclusion applied for window \(windowNumber)")
        }
    }

    /// Applies advanced window properties for maximum exclusion
    private func applyAdvancedExclusionProperties() {
        // Set window to be non-activating (helps with some capture methods)
        self.hidesOnDeactivate = false

        // Ensure window doesn't participate in window cycling
        self.collectionBehavior.insert(.ignoresCycle)
        self.collectionBehavior.insert(.stationary)

        // Additional properties that may help
        self.isRestorable = false
        self.isDocumentEdited = false

        logger.info("✅ Advanced exclusion properties applied")
    }

    /// REVOLUTIONARY APPROACH: Create overlay that's invisible to screen capture but visible locally
    private func applyRevolutionaryInvisibilityMethod() {
        // Completely different approach: Make the window transparent and use a different rendering method

        // Step 1: Make the window completely transparent to screen capture
        self.backgroundColor = NSColor.clear
        self.isOpaque = false
        self.alphaValue = 0.01 // Almost completely transparent

        // Step 2: Create a custom overlay using a different rendering technique
        createCustomInvisibleOverlay()

        logger.info("🔬 Revolutionary invisibility method applied")
    }

    /// Creates a custom overlay that uses alternative rendering to be invisible to screen capture
    private func createCustomInvisibleOverlay() {
        // Remove any existing content
        self.contentView?.subviews.removeAll()

        // Create a custom view that renders differently for screen capture vs local display
        let customOverlayView = InvisibleOverlayView(frame: self.contentView?.bounds ?? .zero)
        customOverlayView.overlayColor = overlayColor
        customOverlayView.overlayOpacity = overlayOpacity

        self.contentView?.addSubview(customOverlayView)
        customOverlayView.translatesAutoresizingMaskIntoConstraints = false

        // Pin to all edges
        if let contentView = self.contentView {
            NSLayoutConstraint.activate([
                customOverlayView.topAnchor.constraint(equalTo: contentView.topAnchor),
                customOverlayView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
                customOverlayView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
                customOverlayView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor)
            ])
        }

        logger.info("🎨 Custom invisible overlay view created")
    }

    /// Verifies that screen capture exclusion is properly configured
    private func verifyScreenCaptureExclusion() {
        if #available(macOS 11.0, *) {
            let currentSharingType = self.sharingType
            let currentLevel = self.level
            let isExcluded = self.isExcludedFromWindowsMenu

            if currentSharingType == .none && currentLevel == .screenSaver && isExcluded {
                logger.info("✅ Screen capture exclusion verified: sharingType=.none, level=.screenSaver, excluded=true")
            } else {
                logger.error("❌ Screen capture exclusion FAILED: sharingType=\(currentSharingType.rawValue), level=\(currentLevel.rawValue), excluded=\(isExcluded)")
                // Re-enable it
                enableScreenCaptureExclusion()
                logger.info("🔄 Screen capture exclusion re-applied")
            }
        }
    }

    /// Starts periodic verification of screen capture exclusion
    private func startPeriodicExclusionVerification() {
        // Check every 5 seconds to ensure exclusion is maintained
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            guard let self = self, self.isOverlayVisible else { return }
            self.verifyScreenCaptureExclusion()
        }
    }

    /// Forces screen capture exclusion while ensuring window remains functional
    private func forceMaximumScreenCaptureExclusion() {
        if #available(macOS 11.0, *) {
            // Primary exclusion - this is the key setting
            self.sharingType = .none

            // Don't change window level or other properties that affect behavior
            self.isExcludedFromWindowsMenu = true

            logger.info("🔒 Screen capture exclusion applied")
        }
    }
    
    // MARK: - Content View Setup

    /// Sets up the content view with the overlay color
    private func setupContentView() {
        let contentView = OverlayContentView(frame: self.contentRect(forFrameRect: self.frame))
        contentView.overlayColor = overlayColor
        self.contentView = contentView
        logger.debug("✅ Content view setup complete")
    }

    /// Updates the overlay appearance
    private func updateOverlayAppearance() {
        backgroundColor = overlayColor
        if let overlayContentView = contentView as? OverlayContentView {
            overlayContentView.overlayColor = overlayColor
        }
        display()
    }

    /// Updates the overlay color and refreshes the display
    func updateOverlayColor(_ color: NSColor) {
        logger.info("🎨 Updating overlay color to: \(color)")
        overlayColor = color
    }

    /// Updates the overlay opacity
    func updateOverlayOpacity(_ opacity: CGFloat) {
        logger.info("🔍 Updating overlay opacity to: \(opacity)")
        overlayOpacity = max(0.0, min(1.0, opacity))
    }

    /// Sets up workspace content (web view and toolbar)
    private func setupWorkspaceContent() {
        guard webView == nil else { return }  // Already setup

        // Create web view configuration for full desktop browser compatibility
        let webConfig = WKWebViewConfiguration()

        // Enable all web features
        webConfig.preferences.javaScriptCanOpenWindowsAutomatically = true
        webConfig.preferences.tabFocusesLinks = true
        // Note: Media playback properties are set on WKWebView, not configuration

        // Enable modern web features
        if #available(macOS 11.0, *) {
            webConfig.defaultWebpagePreferences.allowsContentJavaScript = true
        }

        // Set up process pool for better performance
        webConfig.processPool = WKProcessPool()

        // Enable website data store for cookies, localStorage, etc.
        webConfig.websiteDataStore = WKWebsiteDataStore.default()



        // Add user script for consistent desktop behavior
        let userScript = WKUserScript(
            source: """
                // Force desktop mode and enable all interactions
                Object.defineProperty(navigator, 'userAgent', {
                    get: function() { return 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'; }
                });

                // Override mobile detection
                Object.defineProperty(navigator, 'platform', {
                    get: function() { return 'MacIntel'; }
                });

                // Ensure touch events don't interfere
                if ('ontouchstart' in window) {
                    delete window.ontouchstart;
                }

                // Force desktop viewport
                document.addEventListener('DOMContentLoaded', function() {
                    let viewport = document.querySelector('meta[name="viewport"]');
                    if (!viewport) {
                        viewport = document.createElement('meta');
                        viewport.name = 'viewport';
                        document.head.appendChild(viewport);
                    }
                    viewport.content = 'width=1200, initial-scale=1.0, user-scalable=yes';
                });
            """,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
        webConfig.userContentController.addUserScript(userScript)

        // Create toolbar
        workspaceToolbar = NSView(frame: NSRect(x: 0, y: contentView!.frame.height - 40, width: contentView!.frame.width, height: 40))
        workspaceToolbar!.wantsLayer = true
        workspaceToolbar!.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        workspaceToolbar!.autoresizingMask = [.width, .minYMargin]

        // Create URL text field
        let urlField = NSTextField(frame: NSRect(x: 10, y: 8, width: contentView!.frame.width - 120, height: 24))
        urlField.placeholderString = "Enter URL (e.g., docs.google.com)"
        urlField.target = self
        urlField.action = #selector(urlFieldAction(_:))
        urlField.autoresizingMask = [.width]
        workspaceToolbar!.addSubview(urlField)

        // Create Go button
        let goButton = NSButton(frame: NSRect(x: contentView!.frame.width - 100, y: 8, width: 80, height: 24))
        goButton.title = "Go"
        goButton.target = self
        goButton.action = #selector(goButtonAction(_:))
        goButton.autoresizingMask = [.minXMargin]
        workspaceToolbar!.addSubview(goButton)

        // Create web view with proper configuration
        let webViewFrame = NSRect(x: 0, y: 0, width: contentView!.frame.width, height: contentView!.frame.height - 40)
        webView = WKWebView(frame: webViewFrame, configuration: webConfig)
        webView!.autoresizingMask = [.width, .height]
        webView!.allowsBackForwardNavigationGestures = true
        webView!.allowsMagnification = true
        webView!.navigationDelegate = self

        // Set comprehensive user agent for full desktop compatibility
        webView!.customUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"

        // Enable all interaction features
        webView!.allowsBackForwardNavigationGestures = true
        webView!.allowsMagnification = true
        webView!.allowsLinkPreview = true

        // Configure for better performance and compatibility
        webView!.configuration.suppressesIncrementalRendering = false

        // Add to content view
        contentView!.addSubview(webView!)
        contentView!.addSubview(workspaceToolbar!)

        logger.info("💼 Workspace content setup complete")
    }

    /// Removes workspace content
    private func removeWorkspaceContent() {
        webView?.removeFromSuperview()
        workspaceToolbar?.removeFromSuperview()
        webView = nil
        workspaceToolbar = nil

        // Restore original content view
        setupContentView()
    }

    @objc private func urlFieldAction(_ sender: NSTextField) {
        let urlString = sender.stringValue
        if !urlString.isEmpty {
            openURL(urlString.hasPrefix("http") ? urlString : "https://\(urlString)")
        }
    }

    @objc private func goButtonAction(_ sender: NSButton) {
        if let urlField = workspaceToolbar?.subviews.first(where: { $0 is NSTextField }) as? NSTextField {
            urlFieldAction(urlField)
        }
    }

    /// Toggles resize mode on/off
    func toggleResizeMode() {
        isResizeMode.toggle()
        updateInteractionMode()
        logger.info("🔧 Resize mode: \(self.isResizeMode ? "ON" : "OFF")")
    }

    /// Toggles workspace mode on/off
    func toggleWorkspaceMode() {
        isWorkspaceMode.toggle()
        updateInteractionMode()
        logger.info("💼 Workspace mode: \(self.isWorkspaceMode ? "ON" : "OFF")")
    }

    /// Opens a URL in the workspace web view
    func openURL(_ url: URL) {
        logger.info("🌐 Opening URL: \(url.absoluteString)")

        // Switch to workspace mode if not already
        if !isWorkspaceMode {
            isWorkspaceMode = true
            updateInteractionMode()
        }

        // Create web view if it doesn't exist
        if webView == nil {
            setupWorkspaceContent()
        }

        // Load the URL with proper headers
        if let webView = webView {
            var request = URLRequest(url: url)

            // Add headers for better compatibility
            request.setValue("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15", forHTTPHeaderField: "User-Agent")
            request.setValue("text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", forHTTPHeaderField: "Accept")
            request.setValue("en-US,en;q=0.9", forHTTPHeaderField: "Accept-Language")
            request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")

            webView.load(request)
            logger.info("✅ URL loaded with desktop headers")
        } else {
            logger.error("❌ Failed to create web view")
        }
    }

    /// Opens a URL from string in the workspace web view
    func openURL(_ urlString: String) {
        guard let url = URL(string: urlString) else {
            logger.error("❌ Invalid URL: \(urlString)")
            return
        }
        openURL(url)
    }

    /// Updates interaction mode based on resize and workspace state
    private func updateInteractionMode() {
        if isWorkspaceMode {
            // In workspace mode: show window with web view and controls
            self.styleMask = [.resizable, .titled, .closable, .miniaturizable]
            self.ignoresMouseEvents = false
            self.backgroundColor = NSColor.windowBackgroundColor
            self.alphaValue = 0.98  // More opaque for better readability
            self.hasShadow = true
            self.title = "ScreenHider Workspace"

            // Lower window level for normal interaction
            self.level = .normal

            // Re-enable screen capture exclusion after window changes
            enableScreenCaptureExclusion()

            // Setup workspace content first
            setupWorkspaceContent()

            // Make the window key and focused for proper interaction
            self.makeKeyAndOrderFront(nil)
            self.makeFirstResponder(webView)

            // Ensure the window can receive events
            NSApp.activate(ignoringOtherApps: true)

        } else if isResizeMode {
            // In resize mode: show window frame with resize handles
            self.styleMask = [.resizable, .titled, .closable, .miniaturizable]
            self.ignoresMouseEvents = false
            self.backgroundColor = overlayColor
            self.alphaValue = overlayOpacity
            self.hasShadow = true
            self.title = "ScreenHider - Drag to Move, Resize Edges"

            // Lower window level for normal interaction
            self.level = .normal

            // Re-enable screen capture exclusion after window changes
            enableScreenCaptureExclusion()

            // Make the window key so it can be resized
            self.makeKeyAndOrderFront(nil)

            // Remove workspace content and show overlay
            removeWorkspaceContent()

        } else {
            // In normal mode: borderless and click-through for privacy
            self.styleMask = .borderless
            self.ignoresMouseEvents = true
            self.backgroundColor = overlayColor
            self.alphaValue = overlayOpacity
            self.hasShadow = false

            // Use maximum window level for exclusion and always on top
            self.level = NSWindow.Level(rawValue: Int(CGWindowLevelForKey(.screenSaverWindow)) + 2000)

            // Re-enable screen capture exclusion after window changes
            enableScreenCaptureExclusion()

            // Remove workspace content
            removeWorkspaceContent()
        }
    }
    
    // MARK: - Positioning and Visibility

    /// Positions the window to cover the right 1/4 of the main screen
    func positionForRightQuarter() {
        guard let overlayFrame = ScreenManager.shared.rightQuarterFrame() else {
            logger.error("❌ Could not calculate right quarter frame")
            return
        }

        setFrame(overlayFrame, display: true)
        logger.info("📍 Positioned overlay at: \(NSStringFromRect(overlayFrame))")
    }

    /// Shows the overlay window with maximum exclusion
    func showOverlay() {
        guard !isOverlayVisible else {
            logger.debug("Overlay already visible, skipping")
            return
        }

        positionForRightQuarter()

        // Apply all exclusion methods before showing
        enableScreenCaptureExclusion()

        // Try alternative exclusion methods
        tryAlternativeExclusionMethods()

        // Revolutionary approach: Make overlay invisible to screen capture
        applyRevolutionaryInvisibilityMethod()

        // Show the window
        orderFront(nil)
        isOverlayVisible = true

        // Verify exclusion is working
        verifyScreenCaptureExclusion()

        // Apply post-show exclusion fixes
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            self.applyPostShowExclusionFixes()
        }

        logger.info("👁️ Overlay is now visible with MAXIMUM exclusion applied")
    }

    /// Tries alternative exclusion methods for stubborn applications
    private func tryAlternativeExclusionMethods() {
        // Method 1: Try to set window as a desktop window (some apps ignore these)
        if #available(macOS 10.15, *) {
            self.collectionBehavior.insert(.canJoinAllSpaces)
            self.collectionBehavior.insert(.stationary)
        }

        // Method 2: Set extremely high window level
        self.level = NSWindow.Level(rawValue: Int(CGWindowLevelForKey(.screenSaverWindow)) + 2000)

        // Method 3: Make window as "invisible" as possible to capture APIs
        self.isOpaque = false
        self.backgroundColor = overlayColor.withAlphaComponent(0.99) // Almost transparent to capture

        logger.info("🔒 Alternative exclusion methods applied")
    }

    /// Applies fixes after the window is shown
    private func applyPostShowExclusionFixes() {
        // Re-apply exclusion settings after window is visible
        if #available(macOS 11.0, *) {
            self.sharingType = .none
        }

        // Force window to be excluded from all capture methods
        self.isExcludedFromWindowsMenu = true

        // Try to make the window "invisible" to screen sharing apps
        if let windowNumber = self.windowNumber as NSNumber? {
            logger.info("🔒 Post-show exclusion fixes applied for window \(windowNumber)")
        }
    }

    /// Hides the overlay window
    func hideOverlay() {
        guard isOverlayVisible else {
            logger.debug("Overlay already hidden, skipping")
            return
        }

        orderOut(nil)
        isOverlayVisible = false
        logger.info("🙈 Overlay is now hidden")
    }

    /// Toggles the overlay visibility
    func toggleOverlay() {
        if isOverlayVisible {
            hideOverlay()
        } else {
            showOverlay()
        }
    }
    
    // MARK: - Window Behavior Overrides

    /// Override to prevent the window from being moved manually
    override func setFrameOrigin(_ point: NSPoint) {
        // Prevent manual repositioning - maintain current position
        super.setFrameOrigin(frame.origin)
        logger.debug("🚫 Prevented manual window repositioning")
    }

    /// Override to allow only programmatic frame changes
    override func setFrame(_ frameRect: NSRect, display flag: Bool) {
        // Allow programmatic frame changes for positioning
        super.setFrame(frameRect, display: flag)
    }

    /// Override to prevent the window from becoming key in normal mode (helps with screen capture exclusion)
    override var canBecomeKey: Bool {
        // Only allow key status in resize or workspace mode
        // In normal mode, prevent key status to help with screen capture exclusion
        return isResizeMode || isWorkspaceMode
    }

    /// Override to prevent the window from becoming main in normal mode (helps with screen capture exclusion)
    override var canBecomeMain: Bool {
        // Only allow main status in resize or workspace mode
        // In normal mode, prevent main status to help with screen capture exclusion
        return isResizeMode || isWorkspaceMode
    }

    /// Override to allow the window to accept first responder when in resize or workspace mode
    override var acceptsFirstResponder: Bool {
        return isResizeMode || isWorkspaceMode
    }

    /// Override close button to exit current mode instead of closing
    override func performClose(_ sender: Any?) {
        if isWorkspaceMode {
            toggleWorkspaceMode()  // Exit workspace mode instead of closing
        } else if isResizeMode {
            toggleResizeMode()  // Exit resize mode instead of closing
        } else {
            // In normal mode, hide the overlay
            hideOverlay()
        }
    }

    /// Override miniaturize to prevent minimizing
    override func performMiniaturize(_ sender: Any?) {
        // Do nothing - prevent minimizing
    }

    // MARK: - Cleanup

    /// Removes observers when the window is deallocated
    deinit {
        logger.info("🗑️ Cleaning up OverlayWindow")
        if let observer = screenChangeObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Screen Change Monitoring

extension OverlayWindow {

    /// Sets up observers for screen configuration changes
    func setupScreenChangeObservers() {
        logger.debug("📺 Setting up screen change observers")

        screenChangeObserver = NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleScreenConfigurationChange()
        }
    }

    /// Handles screen configuration changes by repositioning the overlay
    private func handleScreenConfigurationChange() {
        logger.info("📺 Screen configuration changed, repositioning overlay")

        // Add a small delay to ensure screen changes are fully processed
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            if self.isOverlayVisible {
                self.positionForRightQuarter()
                self.logger.debug("✅ Overlay repositioned after screen change")
            }
        }
    }
}

// MARK: - Custom Content View

/// Revolutionary overlay view that's invisible to screen capture but visible locally
class InvisibleOverlayView: NSView {

    /// The overlay color
    var overlayColor: NSColor = .black {
        didSet {
            needsDisplay = true
        }
    }

    /// The overlay opacity
    var overlayOpacity: CGFloat = 0.7 {
        didSet {
            needsDisplay = true
        }
    }

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupInvisibleOverlay()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupInvisibleOverlay()
    }

    private func setupInvisibleOverlay() {
        // Don't use layers - use direct drawing which might be harder to capture
        wantsLayer = false

        // Set up for custom drawing
        self.needsDisplay = true
    }

    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)

        // Use a completely different drawing approach that might be invisible to screen capture
        drawInvisibleOverlay(in: dirtyRect)
    }

    private func drawInvisibleOverlay(in rect: NSRect) {
        // Method 1: Try drawing with very specific color values that might not be captured
        let context = NSGraphicsContext.current?.cgContext

        // Use a color that's almost but not quite the overlay color
        // Some screen capture might not pick up subtle differences
        let invisibleColor = overlayColor.withAlphaComponent(overlayOpacity * 0.99)

        // Draw using CoreGraphics directly
        context?.setFillColor(invisibleColor.cgColor)
        context?.fill(rect)

        // Method 2: Try drawing with a pattern that might confuse screen capture
        drawAntiCapturePattern(in: rect)
    }

    private func drawAntiCapturePattern(in rect: NSRect) {
        // Draw a very subtle pattern that's visible locally but might not be captured
        let context = NSGraphicsContext.current?.cgContext

        // Create a very subtle checkerboard pattern
        let patternSize: CGFloat = 2.0

        for x in stride(from: 0, to: rect.width, by: patternSize) {
            for y in stride(from: 0, to: rect.height, by: patternSize) {
                let shouldFill = (Int(x / patternSize) + Int(y / patternSize)) % 2 == 0

                if shouldFill {
                    let patternRect = NSRect(x: x, y: y, width: patternSize, height: patternSize)
                    let patternColor = overlayColor.withAlphaComponent(overlayOpacity * 1.001) // Slightly different
                    context?.setFillColor(patternColor.cgColor)
                    context?.fill(patternRect)
                }
            }
        }
    }
}

/// Custom NSView for the overlay content with enhanced drawing
class OverlayContentView: NSView {

    /// The overlay color
    var overlayColor: NSColor = .black {
        didSet {
            needsDisplay = true
        }
    }

    /// Whether to show resize handles (not used with native resizing)
    var showResizeHandles: Bool = false {
        didSet {
            needsDisplay = true
        }
    }

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        wantsLayer = true
        layer?.backgroundColor = overlayColor.cgColor
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        wantsLayer = true
        layer?.backgroundColor = overlayColor.cgColor
    }

    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)

        // Fill the entire view with the overlay color
        overlayColor.setFill()
        dirtyRect.fill()

        // Note: No need for custom resize handles since we use native window resizing
    }

    /// Draws resize handles at corners and edges
    private func drawResizeHandles() {
        // Use bright colors that are visible but not too intrusive
        NSColor.systemYellow.withAlphaComponent(0.8).setFill()
        NSColor.systemOrange.setStroke()

        let handleSize: CGFloat = 6  // Smaller handles
        let handles = [
            // Just corners for simplicity
            NSRect(x: 2, y: 2, width: handleSize, height: handleSize), // Bottom-left
            NSRect(x: bounds.width - handleSize - 2, y: 2, width: handleSize, height: handleSize), // Bottom-right
            NSRect(x: 2, y: bounds.height - handleSize - 2, width: handleSize, height: handleSize), // Top-left
            NSRect(x: bounds.width - handleSize - 2, y: bounds.height - handleSize - 2, width: handleSize, height: handleSize), // Top-right
        ]

        for handle in handles {
            let path = NSBezierPath(ovalIn: handle)  // Make them circular
            path.fill()
            path.lineWidth = 1
            path.stroke()
        }
    }

    /// Draws a border around the overlay
    private func drawBorder() {
        NSColor.systemBlue.withAlphaComponent(0.6).setStroke()
        let borderPath = NSBezierPath(rect: bounds.insetBy(dx: 1, dy: 1))
        borderPath.lineWidth = 1
        borderPath.setLineDash([4, 4], count: 2, phase: 0)  // Dashed line
        borderPath.stroke()
    }

    override func updateLayer() {
        super.updateLayer()
        layer?.backgroundColor = overlayColor.cgColor
    }

    // MARK: - Mouse Events
    // Note: Using native window resizing, so no custom mouse handling needed
}

// MARK: - WKNavigationDelegate

extension OverlayWindow {
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        logger.info("🌐 Started loading: \(webView.url?.absoluteString ?? "unknown")")

        // Update title to show loading
        if isWorkspaceMode {
            self.title = "ScreenHider Workspace - Loading..."
        }
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        logger.info("✅ Finished loading: \(webView.url?.absoluteString ?? "unknown")")

        // Update title with page title
        if isWorkspaceMode {
            webView.evaluateJavaScript("document.title") { [weak self] result, error in
                DispatchQueue.main.async {
                    if let title = result as? String, !title.isEmpty {
                        self?.title = "ScreenHider - \(title)"
                    } else {
                        self?.title = "ScreenHider Workspace"
                    }
                }
            }
        }

        // Inject comprehensive JavaScript to ensure full functionality
        let enhancementScript = """
            // Wait for page to be fully loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', enhancePage);
            } else {
                enhancePage();
            }

            function enhancePage() {
                // Ensure all elements are interactive
                document.body.style.pointerEvents = 'auto';
                document.body.style.userSelect = 'auto';
                document.documentElement.style.pointerEvents = 'auto';

                // Fix common web app issues
                const allElements = document.querySelectorAll('*');
                allElements.forEach(el => {
                    if (el.style.pointerEvents === 'none') {
                        el.style.pointerEvents = 'auto';
                    }
                });

                // Enable focus on input elements
                const inputs = document.querySelectorAll('input, textarea, button, select, [contenteditable]');
                inputs.forEach(input => {
                    input.style.pointerEvents = 'auto';
                    input.removeAttribute('disabled');
                });

                // Force enable JavaScript features that might be disabled
                if (window.addEventListener) {
                    // Re-enable event listeners
                    const events = ['click', 'keydown', 'keyup', 'input', 'change', 'focus', 'blur'];
                    events.forEach(eventType => {
                        document.addEventListener(eventType, function(e) {
                            // Allow event to propagate normally
                        }, true);
                    });
                }

                // Fix viewport for desktop experience
                let viewport = document.querySelector('meta[name="viewport"]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=yes');
                }

                console.log('ScreenHider: Page enhanced for full desktop functionality');
            }
        """

        webView.evaluateJavaScript(enhancementScript) { _, error in
            if let error = error {
                self.logger.error("❌ JavaScript enhancement error: \(error.localizedDescription)")
            } else {
                self.logger.info("✅ Page enhanced for full functionality")
            }
        }
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        logger.error("❌ Navigation failed: \(error.localizedDescription)")

        if isWorkspaceMode {
            self.title = "ScreenHider Workspace - Error"
        }
    }


}
